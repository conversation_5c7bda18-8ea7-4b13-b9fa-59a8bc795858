// ملف Preload لـ Electron - يوفر واجهة آمنة بين العملية الرئيسية والعملية المعروضة

const { contextBridge, ipcRenderer } = require('electron')

// تعريض APIs آمنة للعملية المعروضة
contextBridge.exposeInMainWorld('electronAPI', {
  // وظائف النظام
  platform: process.platform,
  
  // وظائف الملفات (يمكن إضافتها لاحقاً)
  openFile: () => ipcRenderer.invoke('dialog:openFile'),
  saveFile: (content) => ipcRenderer.invoke('dialog:saveFile', content),
  
  // وظائف الطباعة (يمكن إضافتها لاحقاً)
  print: (content) => ipcRenderer.invoke('print:content', content),
  
  // وظائف النوافذ
  minimize: () => ipcRenderer.invoke('window:minimize'),
  maximize: () => ipcRenderer.invoke('window:maximize'),
  close: () => ipcRenderer.invoke('window:close'),
  
  // الإشعارات
  showNotification: (title, body) => ipcRenderer.invoke('notification:show', title, body),
  
  // معلومات التطبيق
  getVersion: () => ipcRenderer.invoke('app:getVersion'),
  
  // وظائف قاعدة البيانات المحلية (يمكن إضافتها لاحقاً)
  // localDB: {
  //   get: (key) => ipcRenderer.invoke('localDB:get', key),
  //   set: (key, value) => ipcRenderer.invoke('localDB:set', key, value),
  //   delete: (key) => ipcRenderer.invoke('localDB:delete', key)
  // }
})

// معالجة الأحداث
window.addEventListener('DOMContentLoaded', () => {
  // يمكن إضافة منطق إضافي هنا عند تحميل الصفحة
  console.log('Electron Preload Script Loaded')
})
