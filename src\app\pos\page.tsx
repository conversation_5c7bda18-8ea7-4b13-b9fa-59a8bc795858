'use client'

import { useState, useEffect } from 'react'
import {
  ShoppingCart,
  Plus,
  Minus,
  Trash2,
  Search,
  CreditCard,
  Banknote,
  Smartphone,
  Calculator,
  Package
} from 'lucide-react'
import Link from 'next/link'
import toast from 'react-hot-toast'

interface Product {
  id: string
  name: string
  price: number
  stock_quantity: number
  barcode?: string
  image_url?: string
}

interface CartItem extends Product {
  quantity: number
  total: number
}

export default function POSPage() {
  const [products, setProducts] = useState<Product[]>([])
  const [cart, setCart] = useState<CartItem[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [paymentMethod, setPaymentMethod] = useState<'cash' | 'card' | 'transfer'>('cash')
  const [customerPaid, setCustomerPaid] = useState<number>(0)
  const [discount, setDiscount] = useState<number>(0)

  // بيانات تجريبية للمنتجات
  useEffect(() => {
    const mockProducts: Product[] = [
      { id: '1', name: 'قهوة عربية', price: 25.00, stock_quantity: 50, barcode: '123456789' },
      { id: '2', name: 'شاي أحمر', price: 15.00, stock_quantity: 30 },
      { id: '3', name: 'عصير برتقال', price: 8.50, stock_quantity: 25 },
      { id: '4', name: 'ماء معدني', price: 2.00, stock_quantity: 100 },
      { id: '5', name: 'بسكويت', price: 12.00, stock_quantity: 40 },
      { id: '6', name: 'شوكولاتة', price: 18.00, stock_quantity: 20 },
    ]
    setProducts(mockProducts)
  }, [])

  const filteredProducts = products.filter(product =>
    product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (product.barcode && product.barcode.includes(searchTerm))
  )

  const addToCart = (product: Product) => {
    if (product.stock_quantity <= 0) {
      toast.error('المنتج غير متوفر في المخزون')
      return
    }

    const existingItem = cart.find(item => item.id === product.id)
    
    if (existingItem) {
      if (existingItem.quantity >= product.stock_quantity) {
        toast.error('الكمية المطلوبة تتجاوز المخزون المتاح')
        return
      }
      updateQuantity(product.id, existingItem.quantity + 1)
    } else {
      const newItem: CartItem = {
        ...product,
        quantity: 1,
        total: product.price
      }
      setCart([...cart, newItem])
      toast.success(`تم إضافة ${product.name} إلى السلة`)
    }
  }

  const updateQuantity = (productId: string, newQuantity: number) => {
    if (newQuantity <= 0) {
      removeFromCart(productId)
      return
    }

    const product = products.find(p => p.id === productId)
    if (product && newQuantity > product.stock_quantity) {
      toast.error('الكمية المطلوبة تتجاوز المخزون المتاح')
      return
    }

    setCart(cart.map(item => 
      item.id === productId 
        ? { ...item, quantity: newQuantity, total: item.price * newQuantity }
        : item
    ))
  }

  const removeFromCart = (productId: string) => {
    setCart(cart.filter(item => item.id !== productId))
    toast.success('تم حذف المنتج من السلة')
  }

  const clearCart = () => {
    setCart([])
    setCustomerPaid(0)
    setDiscount(0)
    toast.success('تم مسح السلة')
  }

  const subtotal = cart.reduce((sum, item) => sum + item.total, 0)
  const discountAmount = (subtotal * discount) / 100
  const tax = (subtotal - discountAmount) * 0.15 // ضريبة 15%
  const total = subtotal - discountAmount + tax
  const change = customerPaid - total

  const handleCheckout = () => {
    if (cart.length === 0) {
      toast.error('السلة فارغة')
      return
    }

    if (paymentMethod === 'cash' && customerPaid < total) {
      toast.error('المبلغ المدفوع أقل من الإجمالي')
      return
    }

    // هنا يمكن إضافة منطق حفظ البيع في قاعدة البيانات
    toast.success('تم إتمام عملية البيع بنجاح!')
    clearCart()
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Link href="/" className="text-blue-600 hover:text-blue-800 ml-4">
                ← العودة للرئيسية
              </Link>
              <h1 className="text-xl font-bold text-gray-900">نقطة البيع</h1>
            </div>
            <div className="flex items-center space-x-4 space-x-reverse">
              <span className="text-sm text-gray-500">إجمالي السلة: {total.toFixed(2)} ر.س</span>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Products Section */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-lg font-semibold text-gray-900">المنتجات</h2>
                <div className="flex items-center space-x-4 space-x-reverse">
                  <div className="relative">
                    <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <input
                      type="text"
                      placeholder="البحث عن منتج أو باركود..."
                      className="form-input pr-10"
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                    />
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                {filteredProducts.map((product) => (
                  <div
                    key={product.id}
                    className="border rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer"
                    onClick={() => addToCart(product)}
                  >
                    <div className="aspect-square bg-gray-100 rounded-lg mb-3 flex items-center justify-center">
                      <Package className="h-8 w-8 text-gray-400" />
                    </div>
                    <h3 className="font-medium text-gray-900 mb-1">{product.name}</h3>
                    <p className="text-lg font-bold text-blue-600 mb-1">{product.price.toFixed(2)} ر.س</p>
                    <p className="text-sm text-gray-500">المخزون: {product.stock_quantity}</p>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Cart Section */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow p-6 sticky top-6">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-lg font-semibold text-gray-900 flex items-center">
                  <ShoppingCart className="h-5 w-5 ml-2" />
                  السلة ({cart.length})
                </h2>
                {cart.length > 0 && (
                  <button
                    onClick={clearCart}
                    className="text-red-600 hover:text-red-800 text-sm"
                  >
                    مسح الكل
                  </button>
                )}
              </div>

              <div className="space-y-3 mb-6 max-h-64 overflow-y-auto">
                {cart.length === 0 ? (
                  <p className="text-gray-500 text-center py-8">السلة فارغة</p>
                ) : (
                  cart.map((item) => (
                    <div key={item.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div className="flex-1">
                        <h4 className="font-medium text-gray-900">{item.name}</h4>
                        <p className="text-sm text-gray-600">{item.price.toFixed(2)} ر.س × {item.quantity}</p>
                      </div>
                      <div className="flex items-center space-x-2 space-x-reverse">
                        <button
                          onClick={() => updateQuantity(item.id, item.quantity - 1)}
                          className="p-1 text-gray-600 hover:text-gray-800"
                        >
                          <Minus className="h-4 w-4" />
                        </button>
                        <span className="w-8 text-center">{item.quantity}</span>
                        <button
                          onClick={() => updateQuantity(item.id, item.quantity + 1)}
                          className="p-1 text-gray-600 hover:text-gray-800"
                        >
                          <Plus className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => removeFromCart(item.id)}
                          className="p-1 text-red-600 hover:text-red-800 mr-2"
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                  ))
                )}
              </div>

              {cart.length > 0 && (
                <>
                  {/* Discount */}
                  <div className="mb-4">
                    <label className="form-label">خصم (%)</label>
                    <input
                      type="number"
                      min="0"
                      max="100"
                      className="form-input"
                      value={discount}
                      onChange={(e) => setDiscount(Number(e.target.value))}
                    />
                  </div>

                  {/* Payment Method */}
                  <div className="mb-4">
                    <label className="form-label">طريقة الدفع</label>
                    <div className="grid grid-cols-3 gap-2">
                      <button
                        onClick={() => setPaymentMethod('cash')}
                        className={`p-2 rounded-lg border text-sm flex flex-col items-center ${
                          paymentMethod === 'cash' 
                            ? 'border-blue-500 bg-blue-50 text-blue-700' 
                            : 'border-gray-300'
                        }`}
                      >
                        <Banknote className="h-4 w-4 mb-1" />
                        نقدي
                      </button>
                      <button
                        onClick={() => setPaymentMethod('card')}
                        className={`p-2 rounded-lg border text-sm flex flex-col items-center ${
                          paymentMethod === 'card' 
                            ? 'border-blue-500 bg-blue-50 text-blue-700' 
                            : 'border-gray-300'
                        }`}
                      >
                        <CreditCard className="h-4 w-4 mb-1" />
                        بطاقة
                      </button>
                      <button
                        onClick={() => setPaymentMethod('transfer')}
                        className={`p-2 rounded-lg border text-sm flex flex-col items-center ${
                          paymentMethod === 'transfer' 
                            ? 'border-blue-500 bg-blue-50 text-blue-700' 
                            : 'border-gray-300'
                        }`}
                      >
                        <Smartphone className="h-4 w-4 mb-1" />
                        تحويل
                      </button>
                    </div>
                  </div>

                  {/* Cash Payment */}
                  {paymentMethod === 'cash' && (
                    <div className="mb-4">
                      <label className="form-label">المبلغ المدفوع</label>
                      <input
                        type="number"
                        step="0.01"
                        className="form-input"
                        value={customerPaid}
                        onChange={(e) => setCustomerPaid(Number(e.target.value))}
                      />
                    </div>
                  )}

                  {/* Summary */}
                  <div className="border-t pt-4 space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>المجموع الفرعي:</span>
                      <span>{subtotal.toFixed(2)} ر.س</span>
                    </div>
                    {discount > 0 && (
                      <div className="flex justify-between text-sm text-green-600">
                        <span>الخصم ({discount}%):</span>
                        <span>-{discountAmount.toFixed(2)} ر.س</span>
                      </div>
                    )}
                    <div className="flex justify-between text-sm">
                      <span>الضريبة (15%):</span>
                      <span>{tax.toFixed(2)} ر.س</span>
                    </div>
                    <div className="flex justify-between font-bold text-lg border-t pt-2">
                      <span>الإجمالي:</span>
                      <span>{total.toFixed(2)} ر.س</span>
                    </div>
                    {paymentMethod === 'cash' && customerPaid > 0 && (
                      <div className="flex justify-between text-sm">
                        <span>الباقي:</span>
                        <span className={change >= 0 ? 'text-green-600' : 'text-red-600'}>
                          {change.toFixed(2)} ر.س
                        </span>
                      </div>
                    )}
                  </div>

                  <button
                    onClick={handleCheckout}
                    className="w-full btn-primary mt-4 flex items-center justify-center"
                  >
                    <Calculator className="h-4 w-4 ml-2" />
                    إتمام عملية البيع
                  </button>
                </>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
