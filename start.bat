@echo off
echo ========================================
echo       نظام نقطة البيع - تشغيل سريع
echo ========================================
echo.

echo جاري التحقق من Node.js...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo خطأ: Node.js غير مثبت!
    echo يرجى تحميل وتثبيت Node.js من: https://nodejs.org
    pause
    exit /b 1
)

echo Node.js مثبت بنجاح!
echo.

echo جاري التحقق من المكتبات...
if not exist "node_modules" (
    echo جاري تثبيت المكتبات...
    npm install
    if %errorlevel% neq 0 (
        echo خطأ في تثبيت المكتبات!
        pause
        exit /b 1
    )
)

echo جاري التحقق من ملف الإعدادات...
if not exist ".env.local" (
    echo تحذير: ملف .env.local غير موجود!
    echo يرجى نسخ .env.local.example إلى .env.local وإدخال بيانات Supabase
    echo.
    echo هل تريد المتابعة بدون إعدادات قاعدة البيانات؟ (y/n)
    set /p continue=
    if /i not "%continue%"=="y" (
        echo يرجى إعداد ملف .env.local أولاً
        pause
        exit /b 1
    )
)

echo.
echo اختر طريقة التشغيل:
echo 1. تطبيق الويب (متصفح)
echo 2. تطبيق سطح المكتب (Electron)
echo 3. إلغاء
echo.
set /p choice=أدخل اختيارك (1-3): 

if "%choice%"=="1" (
    echo جاري تشغيل تطبيق الويب...
    echo سيتم فتح المتصفح على http://localhost:3000
    start http://localhost:3000
    npm run dev
) else if "%choice%"=="2" (
    echo جاري تشغيل تطبيق سطح المكتب...
    npm run electron-dev
) else if "%choice%"=="3" (
    echo تم الإلغاء
    exit /b 0
) else (
    echo اختيار غير صحيح!
    pause
    exit /b 1
)

pause
