# دليل التثبيت المفصل - نظام نقطة البيع

## المتطلبات الأساسية

### 1. تثبيت Node.js
- قم بزيارة [nodejs.org](https://nodejs.org)
- حمل النسخة LTS (الموصى بها)
- قم بتثبيت Node.js على نظامك
- تأكد من التثبيت بتشغيل الأوامر التالية في Command Prompt:
```bash
node --version
npm --version
```

### 2. إنشاء حساب Supabase
- اذهب إلى [supabase.com](https://supabase.com)
- أنشئ حساب جديد (مجاني)
- أنشئ مشروع جديد
- احفظ URL المشروع و API Key

## خطوات التثبيت

### الخطوة 1: تحميل المشروع
```bash
# إذا كان لديك Git
git clone <repository-url>
cd pos-system

# أو قم بتحميل الملفات وفك الضغط
```

### الخطوة 2: تثبيت المكتبات
```bash
npm install
```

### الخطوة 3: إعداد متغيرات البيئة
1. انسخ ملف `.env.local.example` إلى `.env.local`
2. افتح `.env.local` وأدخل بيانات Supabase:
```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
```

### الخطوة 4: إعداد قاعدة البيانات
1. اذهب إلى لوحة تحكم Supabase
2. اختر مشروعك
3. اذهب إلى SQL Editor
4. انسخ محتوى ملف `database/schema.sql` والصقه
5. اضغط Run لتنفيذ الاستعلامات

### الخطوة 5: تشغيل التطبيق

#### للتطوير (متصفح):
```bash
npm run dev
```
ثم اذهب إلى http://localhost:3000

#### لتطبيق سطح المكتب:
```bash
npm run electron-dev
```

### الخطوة 6: بناء التطبيق للإنتاج
```bash
# بناء تطبيق الويب
npm run build

# بناء تطبيق سطح المكتب
npm run electron-build
```

## استكشاف الأخطاء

### مشكلة: "node is not recognized"
**الحل:** تأكد من تثبيت Node.js وإعادة تشغيل Command Prompt

### مشكلة: "npm install fails"
**الحل:** 
```bash
# امسح مجلد node_modules وملف package-lock.json
rm -rf node_modules package-lock.json
npm install
```

### مشكلة: خطأ في الاتصال بـ Supabase
**الحل:** تأكد من:
- صحة URL و API Key في `.env.local`
- تشغيل استعلامات قاعدة البيانات
- تفعيل Row Level Security

### مشكلة: التطبيق لا يعمل على Electron
**الحل:**
```bash
# تأكد من تثبيت Electron
npm install electron --save-dev
npm run electron-dev
```

## الميزات المتاحة

### ✅ مكتملة
- الصفحة الرئيسية مع الإحصائيات
- نقطة البيع مع سلة التسوق
- إدارة المنتجات (عرض، إضافة، تعديل، حذف)
- إدارة العملاء
- التقارير والإحصائيات
- الإعدادات العامة
- تطبيق سطح مكتب بـ Electron

### 🔄 قيد التطوير
- نظام المصادقة
- طباعة الفواتير
- قارئ الباركود
- النسخ الاحتياطي التلقائي
- إدارة المستخدمين والصلاحيات

## الاستخدام الأساسي

### 1. إضافة منتجات
- اذهب إلى "إدارة المنتجات"
- اضغط "إضافة منتج جديد"
- املأ البيانات واحفظ

### 2. إجراء عملية بيع
- اذهب إلى "نقطة البيع"
- اختر المنتجات لإضافتها للسلة
- اختر طريقة الدفع
- اضغط "إتمام عملية البيع"

### 3. عرض التقارير
- اذهب إلى "التقارير"
- اختر الفترة الزمنية
- اعرض الإحصائيات والتقارير

## الدعم الفني

إذا واجهت أي مشاكل:
1. تأكد من اتباع جميع خطوات التثبيت
2. تحقق من أن جميع المتطلبات مثبتة
3. راجع قسم استكشاف الأخطاء
4. أنشئ issue في GitHub مع تفاصيل المشكلة

## التحديثات المستقبلية

سيتم إضافة المزيد من الميزات في التحديثات القادمة:
- تطبيق موبايل
- تكامل مع أنظمة الدفع الإلكتروني
- تقارير متقدمة
- إدارة الموردين
- نظام الولاء للعملاء

---

**ملاحظة:** هذا المشروع في مرحلة التطوير. يُنصح بعمل نسخة احتياطية من البيانات بانتظام.
