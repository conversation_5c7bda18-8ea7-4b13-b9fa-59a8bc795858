'use client'

import { useState } from 'react'
import { 
  Settings, 
  Store, 
  User, 
  Bell, 
  Printer, 
  Database,
  Shield,
  Palette,
  Save,
  RefreshCw
} from 'lucide-react'
import Link from 'next/link'
import toast from 'react-hot-toast'

interface StoreSettings {
  name: string
  address: string
  phone: string
  email: string
  tax_number: string
  currency: string
  tax_rate: number
}

interface PrinterSettings {
  receipt_printer: string
  paper_size: string
  print_logo: boolean
  print_footer: boolean
  footer_text: string
}

interface SystemSettings {
  low_stock_threshold: number
  auto_backup: boolean
  backup_frequency: string
  language: string
  theme: string
}

export default function SettingsPage() {
  const [activeTab, setActiveTab] = useState<'store' | 'printer' | 'system' | 'users'>('store')
  const [loading, setLoading] = useState(false)

  const [storeSettings, setStoreSettings] = useState<StoreSettings>({
    name: 'متجر الأمل',
    address: 'الرياض، حي النخيل، شارع الملك فهد',
    phone: '0112345678',
    email: '<EMAIL>',
    tax_number: '123456789012345',
    currency: 'SAR',
    tax_rate: 15
  })

  const [printerSettings, setPrinterSettings] = useState<PrinterSettings>({
    receipt_printer: 'default',
    paper_size: '80mm',
    print_logo: true,
    print_footer: true,
    footer_text: 'شكراً لزيارتكم - نتطلع لخدمتكم مرة أخرى'
  })

  const [systemSettings, setSystemSettings] = useState<SystemSettings>({
    low_stock_threshold: 10,
    auto_backup: true,
    backup_frequency: 'daily',
    language: 'ar',
    theme: 'light'
  })

  const handleSaveSettings = async () => {
    setLoading(true)
    try {
      // هنا يمكن إضافة منطق حفظ الإعدادات
      await new Promise(resolve => setTimeout(resolve, 1000))
      toast.success('تم حفظ الإعدادات بنجاح!')
    } catch (error) {
      toast.error('حدث خطأ أثناء حفظ الإعدادات')
    } finally {
      setLoading(false)
    }
  }

  const tabs = [
    { id: 'store', name: 'إعدادات المتجر', icon: Store },
    { id: 'printer', name: 'إعدادات الطباعة', icon: Printer },
    { id: 'system', name: 'إعدادات النظام', icon: Settings },
    { id: 'users', name: 'إدارة المستخدمين', icon: User }
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Link href="/" className="text-blue-600 hover:text-blue-800 ml-4">
                ← العودة للرئيسية
              </Link>
              <h1 className="text-xl font-bold text-gray-900">الإعدادات</h1>
            </div>
            <button
              onClick={handleSaveSettings}
              disabled={loading}
              className="btn-primary inline-flex items-center"
            >
              {loading ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2"></div>
              ) : (
                <Save className="h-4 w-4 ml-2" />
              )}
              حفظ التغييرات
            </button>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Sidebar */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow p-6">
              <nav className="space-y-2">
                {tabs.map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id as any)}
                    className={`w-full flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                      activeTab === tab.id
                        ? 'bg-blue-100 text-blue-700'
                        : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                    }`}
                  >
                    <tab.icon className="h-4 w-4 ml-3" />
                    {tab.name}
                  </button>
                ))}
              </nav>
            </div>
          </div>

          {/* Content */}
          <div className="lg:col-span-3">
            <div className="bg-white rounded-lg shadow p-6">
              {/* Store Settings */}
              {activeTab === 'store' && (
                <div>
                  <div className="flex items-center mb-6">
                    <Store className="h-6 w-6 text-blue-600 ml-3" />
                    <h2 className="text-lg font-semibold text-gray-900">إعدادات المتجر</h2>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="form-label">اسم المتجر *</label>
                      <input
                        type="text"
                        className="form-input"
                        value={storeSettings.name}
                        onChange={(e) => setStoreSettings({...storeSettings, name: e.target.value})}
                      />
                    </div>

                    <div>
                      <label className="form-label">رقم الهاتف *</label>
                      <input
                        type="tel"
                        className="form-input"
                        value={storeSettings.phone}
                        onChange={(e) => setStoreSettings({...storeSettings, phone: e.target.value})}
                      />
                    </div>

                    <div className="md:col-span-2">
                      <label className="form-label">العنوان *</label>
                      <textarea
                        className="form-input"
                        rows={3}
                        value={storeSettings.address}
                        onChange={(e) => setStoreSettings({...storeSettings, address: e.target.value})}
                      />
                    </div>

                    <div>
                      <label className="form-label">البريد الإلكتروني</label>
                      <input
                        type="email"
                        className="form-input"
                        value={storeSettings.email}
                        onChange={(e) => setStoreSettings({...storeSettings, email: e.target.value})}
                      />
                    </div>

                    <div>
                      <label className="form-label">الرقم الضريبي</label>
                      <input
                        type="text"
                        className="form-input"
                        value={storeSettings.tax_number}
                        onChange={(e) => setStoreSettings({...storeSettings, tax_number: e.target.value})}
                      />
                    </div>

                    <div>
                      <label className="form-label">العملة</label>
                      <select
                        className="form-input"
                        value={storeSettings.currency}
                        onChange={(e) => setStoreSettings({...storeSettings, currency: e.target.value})}
                      >
                        <option value="SAR">ريال سعودي (SAR)</option>
                        <option value="AED">درهم إماراتي (AED)</option>
                        <option value="USD">دولار أمريكي (USD)</option>
                      </select>
                    </div>

                    <div>
                      <label className="form-label">معدل الضريبة (%)</label>
                      <input
                        type="number"
                        step="0.01"
                        min="0"
                        max="100"
                        className="form-input"
                        value={storeSettings.tax_rate}
                        onChange={(e) => setStoreSettings({...storeSettings, tax_rate: parseFloat(e.target.value)})}
                      />
                    </div>
                  </div>
                </div>
              )}

              {/* Printer Settings */}
              {activeTab === 'printer' && (
                <div>
                  <div className="flex items-center mb-6">
                    <Printer className="h-6 w-6 text-blue-600 ml-3" />
                    <h2 className="text-lg font-semibold text-gray-900">إعدادات الطباعة</h2>
                  </div>

                  <div className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <label className="form-label">طابعة الفواتير</label>
                        <select
                          className="form-input"
                          value={printerSettings.receipt_printer}
                          onChange={(e) => setPrinterSettings({...printerSettings, receipt_printer: e.target.value})}
                        >
                          <option value="default">الطابعة الافتراضية</option>
                          <option value="thermal">طابعة حرارية</option>
                          <option value="laser">طابعة ليزر</option>
                        </select>
                      </div>

                      <div>
                        <label className="form-label">حجم الورق</label>
                        <select
                          className="form-input"
                          value={printerSettings.paper_size}
                          onChange={(e) => setPrinterSettings({...printerSettings, paper_size: e.target.value})}
                        >
                          <option value="80mm">80 مم</option>
                          <option value="58mm">58 مم</option>
                          <option value="A4">A4</option>
                        </select>
                      </div>
                    </div>

                    <div className="space-y-4">
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          checked={printerSettings.print_logo}
                          onChange={(e) => setPrinterSettings({...printerSettings, print_logo: e.target.checked})}
                          className="ml-2"
                        />
                        طباعة شعار المتجر
                      </label>

                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          checked={printerSettings.print_footer}
                          onChange={(e) => setPrinterSettings({...printerSettings, print_footer: e.target.checked})}
                          className="ml-2"
                        />
                        طباعة نص في أسفل الفاتورة
                      </label>
                    </div>

                    {printerSettings.print_footer && (
                      <div>
                        <label className="form-label">نص أسفل الفاتورة</label>
                        <textarea
                          className="form-input"
                          rows={3}
                          value={printerSettings.footer_text}
                          onChange={(e) => setPrinterSettings({...printerSettings, footer_text: e.target.value})}
                        />
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* System Settings */}
              {activeTab === 'system' && (
                <div>
                  <div className="flex items-center mb-6">
                    <Settings className="h-6 w-6 text-blue-600 ml-3" />
                    <h2 className="text-lg font-semibold text-gray-900">إعدادات النظام</h2>
                  </div>

                  <div className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <label className="form-label">حد المخزون المنخفض</label>
                        <input
                          type="number"
                          min="1"
                          className="form-input"
                          value={systemSettings.low_stock_threshold}
                          onChange={(e) => setSystemSettings({...systemSettings, low_stock_threshold: parseInt(e.target.value)})}
                        />
                        <p className="text-sm text-gray-500 mt-1">
                          سيتم تنبيهك عندما يصل المخزون إلى هذا الحد
                        </p>
                      </div>

                      <div>
                        <label className="form-label">اللغة</label>
                        <select
                          className="form-input"
                          value={systemSettings.language}
                          onChange={(e) => setSystemSettings({...systemSettings, language: e.target.value})}
                        >
                          <option value="ar">العربية</option>
                          <option value="en">English</option>
                        </select>
                      </div>

                      <div>
                        <label className="form-label">المظهر</label>
                        <select
                          className="form-input"
                          value={systemSettings.theme}
                          onChange={(e) => setSystemSettings({...systemSettings, theme: e.target.value})}
                        >
                          <option value="light">فاتح</option>
                          <option value="dark">داكن</option>
                          <option value="auto">تلقائي</option>
                        </select>
                      </div>

                      <div>
                        <label className="form-label">تكرار النسخ الاحتياطي</label>
                        <select
                          className="form-input"
                          value={systemSettings.backup_frequency}
                          onChange={(e) => setSystemSettings({...systemSettings, backup_frequency: e.target.value})}
                        >
                          <option value="daily">يومي</option>
                          <option value="weekly">أسبوعي</option>
                          <option value="monthly">شهري</option>
                        </select>
                      </div>
                    </div>

                    <div className="space-y-4">
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          checked={systemSettings.auto_backup}
                          onChange={(e) => setSystemSettings({...systemSettings, auto_backup: e.target.checked})}
                          className="ml-2"
                        />
                        تفعيل النسخ الاحتياطي التلقائي
                      </label>
                    </div>

                    <div className="border-t pt-6">
                      <h3 className="text-md font-medium text-gray-900 mb-4">إدارة البيانات</h3>
                      <div className="flex space-x-4 space-x-reverse">
                        <button className="btn-secondary inline-flex items-center">
                          <Database className="h-4 w-4 ml-2" />
                          إنشاء نسخة احتياطية
                        </button>
                        <button className="btn-secondary inline-flex items-center">
                          <RefreshCw className="h-4 w-4 ml-2" />
                          استعادة النسخة الاحتياطية
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Users Management */}
              {activeTab === 'users' && (
                <div>
                  <div className="flex items-center justify-between mb-6">
                    <div className="flex items-center">
                      <User className="h-6 w-6 text-blue-600 ml-3" />
                      <h2 className="text-lg font-semibold text-gray-900">إدارة المستخدمين</h2>
                    </div>
                    <button className="btn-primary inline-flex items-center">
                      <User className="h-4 w-4 ml-2" />
                      إضافة مستخدم
                    </button>
                  </div>

                  <div className="bg-gray-50 rounded-lg p-6 text-center">
                    <Shield className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">قريباً</h3>
                    <p className="text-gray-600">
                      ستتمكن قريباً من إدارة المستخدمين وصلاحياتهم في النظام
                    </p>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
