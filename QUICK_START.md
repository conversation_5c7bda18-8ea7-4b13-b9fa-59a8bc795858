# دليل البدء السريع - نظام نقطة البيع

## 🚀 البدء السريع

### الخطوة 1: تثبيت Node.js
1. اذهب إلى [nodejs.org](https://nodejs.org)
2. حمل النسخة LTS (الموصى بها)
3. قم بتثبيت Node.js
4. أعد تشغيل Command Prompt

### الخطوة 2: تشغيل المشروع
```bash
# تثبيت المكتبات
npm install

# تشغيل التطبيق
npm run dev
```

### الخطوة 3: إعداد قاعدة البيانات (اختياري)
1. أنشئ حساب في [Supabase](https://supabase.com)
2. أنشئ مشروع جديد
3. انسخ `.env.local.example` إلى `.env.local`
4. أدخل بيانات Supabase في `.env.local`
5. شغل استعلامات `database/schema.sql` في Supabase

## 📱 الميزات المتاحة

### ✅ جاهزة للاستخدام
- **الصفحة الرئيسية**: لوحة تحكم مع إحصائيات سريعة
- **نقطة البيع**: واجهة بيع تفاعلية مع سلة تسوق
- **إدارة المنتجات**: إضافة وتعديل وحذف المنتجات
- **إدارة العملاء**: قاعدة بيانات العملاء
- **التقارير**: إحصائيات وتقارير مفصلة
- **الإعدادات**: تخصيص إعدادات المتجر

### 🔧 تطبيق سطح المكتب
```bash
# تشغيل كتطبيق سطح مكتب
npm run electron-dev

# بناء التطبيق للتوزيع
npm run electron-build
```

## 🎯 كيفية الاستخدام

### 1. إضافة منتج جديد
- اذهب إلى "إدارة المنتجات"
- اضغط "إضافة منتج جديد"
- املأ البيانات (الاسم، السعر، المخزون)
- احفظ المنتج

### 2. إجراء عملية بيع
- اذهب إلى "نقطة البيع"
- ابحث عن المنتج أو اضغط عليه
- سيتم إضافته للسلة تلقائياً
- اختر طريقة الدفع
- اضغط "إتمام عملية البيع"

### 3. عرض التقارير
- اذهب إلى "التقارير"
- اختر الفترة الزمنية
- اعرض الإحصائيات والمبيعات

## 🛠️ التخصيص

### تغيير إعدادات المتجر
- اذهب إلى "الإعدادات"
- قسم "إعدادات المتجر"
- غير اسم المتجر والعنوان والهاتف
- احفظ التغييرات

### إضافة منتجات بكميات كبيرة
يمكنك تعديل ملف `src/app/products/page.tsx` لإضافة منتجات تجريبية أكثر.

## 📊 البيانات التجريبية

المشروع يأتي مع بيانات تجريبية:
- 5 منتجات مختلفة
- 4 فئات للمنتجات
- 5 عملاء وهميين
- بيانات مبيعات للأسبوع الماضي

## 🔧 استكشاف الأخطاء

### المشروع لا يعمل؟
```bash
# امسح المكتبات وأعد تثبيتها
rm -rf node_modules package-lock.json
npm install
npm run dev
```

### خطأ في قاعدة البيانات؟
- تأكد من إعداد `.env.local` بشكل صحيح
- تأكد من تشغيل استعلامات SQL في Supabase
- المشروع يعمل بدون قاعدة بيانات (بيانات تجريبية)

### تطبيق Electron لا يعمل؟
```bash
npm install electron --save-dev
npm run electron-dev
```

## 🚀 النشر

### تطبيق الويب
```bash
npm run build
# ارفع مجلد 'out' إلى أي خدمة استضافة
```

### تطبيق سطح المكتب
```bash
npm run electron-build
# ستجد الملفات في مجلد 'dist'
```

## 📞 الدعم

إذا واجهت أي مشاكل:
1. راجع ملف `INSTALLATION.md` للتفاصيل الكاملة
2. تأكد من تثبيت Node.js بشكل صحيح
3. أنشئ issue في GitHub

---

**نصيحة**: ابدأ بتشغيل `npm run dev` أولاً لتجربة التطبيق، ثم أضف قاعدة البيانات لاحقاً للحصول على الوظائف الكاملة.
