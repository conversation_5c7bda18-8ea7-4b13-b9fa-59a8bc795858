import { createClient } from '@supabase/supabase-js'

// يجب استبدال هذه القيم بالقيم الفعلية من مشروع Supabase الخاص بك
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'YOUR_SUPABASE_URL'
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'YOUR_SUPABASE_ANON_KEY'

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// أنواع البيانات
export interface Product {
  id: string
  name: string
  description?: string
  price: number
  cost: number
  stock_quantity: number
  barcode?: string
  category_id?: string
  image_url?: string
  created_at: string
  updated_at: string
}

export interface Category {
  id: string
  name: string
  description?: string
  created_at: string
}

export interface Customer {
  id: string
  name: string
  email?: string
  phone?: string
  address?: string
  created_at: string
}

export interface Sale {
  id: string
  customer_id?: string
  total_amount: number
  discount_amount: number
  tax_amount: number
  payment_method: 'cash' | 'card' | 'transfer'
  status: 'completed' | 'pending' | 'cancelled'
  created_at: string
  updated_at: string
}

export interface SaleItem {
  id: string
  sale_id: string
  product_id: string
  quantity: number
  unit_price: number
  total_price: number
  created_at: string
}

// وظائف مساعدة لقاعدة البيانات
export const dbHelpers = {
  // المنتجات
  async getProducts() {
    const { data, error } = await supabase
      .from('products')
      .select(`
        *,
        categories (
          id,
          name
        )
      `)
      .order('name')
    
    if (error) throw error
    return data
  },

  async getProduct(id: string) {
    const { data, error } = await supabase
      .from('products')
      .select('*')
      .eq('id', id)
      .single()
    
    if (error) throw error
    return data
  },

  async createProduct(product: Omit<Product, 'id' | 'created_at' | 'updated_at'>) {
    const { data, error } = await supabase
      .from('products')
      .insert(product)
      .select()
      .single()
    
    if (error) throw error
    return data
  },

  async updateProduct(id: string, updates: Partial<Product>) {
    const { data, error } = await supabase
      .from('products')
      .update({ ...updates, updated_at: new Date().toISOString() })
      .eq('id', id)
      .select()
      .single()
    
    if (error) throw error
    return data
  },

  async deleteProduct(id: string) {
    const { error } = await supabase
      .from('products')
      .delete()
      .eq('id', id)
    
    if (error) throw error
  },

  // العملاء
  async getCustomers() {
    const { data, error } = await supabase
      .from('customers')
      .select('*')
      .order('name')
    
    if (error) throw error
    return data
  },

  async createCustomer(customer: Omit<Customer, 'id' | 'created_at'>) {
    const { data, error } = await supabase
      .from('customers')
      .insert(customer)
      .select()
      .single()
    
    if (error) throw error
    return data
  },

  // المبيعات
  async createSale(sale: Omit<Sale, 'id' | 'created_at' | 'updated_at'>, items: Omit<SaleItem, 'id' | 'sale_id' | 'created_at'>[]) {
    const { data: saleData, error: saleError } = await supabase
      .from('sales')
      .insert(sale)
      .select()
      .single()
    
    if (saleError) throw saleError

    const saleItems = items.map(item => ({
      ...item,
      sale_id: saleData.id
    }))

    const { data: itemsData, error: itemsError } = await supabase
      .from('sale_items')
      .insert(saleItems)
      .select()
    
    if (itemsError) throw itemsError

    // تحديث المخزون
    for (const item of items) {
      await supabase.rpc('update_product_stock', {
        product_id: item.product_id,
        quantity_sold: item.quantity
      })
    }

    return { sale: saleData, items: itemsData }
  },

  async getSales(limit = 50) {
    const { data, error } = await supabase
      .from('sales')
      .select(`
        *,
        customers (
          id,
          name
        ),
        sale_items (
          *,
          products (
            id,
            name
          )
        )
      `)
      .order('created_at', { ascending: false })
      .limit(limit)
    
    if (error) throw error
    return data
  },

  // التقارير
  async getSalesReport(startDate: string, endDate: string) {
    const { data, error } = await supabase
      .from('sales')
      .select('*')
      .gte('created_at', startDate)
      .lte('created_at', endDate)
      .eq('status', 'completed')
    
    if (error) throw error
    return data
  }
}
