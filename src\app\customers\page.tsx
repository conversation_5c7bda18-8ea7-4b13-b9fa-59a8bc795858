'use client'

import { useState, useEffect } from 'react'
import { 
  Users, 
  Plus, 
  Search, 
  Edit, 
  Trash2, 
  Eye,
  Phone,
  Mail,
  MapPin,
  ShoppingBag
} from 'lucide-react'
import Link from 'next/link'
import toast from 'react-hot-toast'

interface Customer {
  id: string
  name: string
  email?: string
  phone?: string
  address?: string
  total_purchases: number
  last_purchase_date?: string
  created_at: string
}

export default function CustomersPage() {
  const [customers, setCustomers] = useState<Customer[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [sortBy, setSortBy] = useState<'name' | 'total_purchases' | 'last_purchase_date' | 'created_at'>('name')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc')

  // بيانات تجريبية للعملاء
  useEffect(() => {
    const mockCustomers: Customer[] = [
      {
        id: '1',
        name: 'أحمد محمد',
        email: '<EMAIL>',
        phone: '0501234567',
        address: 'الرياض، حي النخيل',
        total_purchases: 1250.75,
        last_purchase_date: '2024-01-15T10:30:00Z',
        created_at: '2023-12-01T09:00:00Z'
      },
      {
        id: '2',
        name: 'فاطمة علي',
        phone: '0509876543',
        address: 'جدة، حي الصفا',
        total_purchases: 890.50,
        last_purchase_date: '2024-01-14T14:20:00Z',
        created_at: '2023-11-15T11:00:00Z'
      },
      {
        id: '3',
        name: 'محمد السعد',
        email: '<EMAIL>',
        phone: '0555555555',
        total_purchases: 2100.25,
        last_purchase_date: '2024-01-13T16:45:00Z',
        created_at: '2023-10-20T08:30:00Z'
      },
      {
        id: '4',
        name: 'نورا أحمد',
        email: '<EMAIL>',
        phone: '0544444444',
        address: 'الدمام، حي الشاطئ',
        total_purchases: 450.00,
        last_purchase_date: '2024-01-10T12:15:00Z',
        created_at: '2024-01-05T10:00:00Z'
      },
      {
        id: '5',
        name: 'خالد العتيبي',
        phone: '0533333333',
        total_purchases: 0,
        created_at: '2024-01-16T09:00:00Z'
      }
    ]

    setCustomers(mockCustomers)
  }, [])

  const filteredCustomers = customers
    .filter(customer => {
      const searchLower = searchTerm.toLowerCase()
      return customer.name.toLowerCase().includes(searchLower) ||
             (customer.email && customer.email.toLowerCase().includes(searchLower)) ||
             (customer.phone && customer.phone.includes(searchTerm))
    })
    .sort((a, b) => {
      let aValue: any = a[sortBy]
      let bValue: any = b[sortBy]
      
      if (sortBy === 'last_purchase_date' || sortBy === 'created_at') {
        aValue = aValue ? new Date(aValue).getTime() : 0
        bValue = bValue ? new Date(bValue).getTime() : 0
      }
      
      if (sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1
      } else {
        return aValue < bValue ? 1 : -1
      }
    })

  const handleDeleteCustomer = (customerId: string) => {
    if (confirm('هل أنت متأكد من حذف هذا العميل؟')) {
      setCustomers(customers.filter(c => c.id !== customerId))
      toast.success('تم حذف العميل بنجاح')
    }
  }

  const totalCustomers = customers.length
  const activeCustomers = customers.filter(c => c.total_purchases > 0).length
  const totalRevenue = customers.reduce((sum, c) => sum + c.total_purchases, 0)
  const averageSpending = activeCustomers > 0 ? totalRevenue / activeCustomers : 0

  const formatDate = (dateString?: string) => {
    if (!dateString) return '-'
    return new Date(dateString).toLocaleDateString('ar-SA', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Link href="/" className="text-blue-600 hover:text-blue-800 ml-4">
                ← العودة للرئيسية
              </Link>
              <h1 className="text-xl font-bold text-gray-900">إدارة العملاء</h1>
            </div>
            <Link
              href="/customers/add"
              className="btn-primary inline-flex items-center"
            >
              <Plus className="h-4 w-4 ml-2" />
              إضافة عميل جديد
            </Link>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Users className="h-6 w-6 text-blue-600" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">إجمالي العملاء</p>
                <p className="text-2xl font-bold text-gray-900">{totalCustomers}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 rounded-lg">
                <ShoppingBag className="h-6 w-6 text-green-600" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">عملاء نشطون</p>
                <p className="text-2xl font-bold text-gray-900">{activeCustomers}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-2 bg-purple-100 rounded-lg">
                <Users className="h-6 w-6 text-purple-600" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">إجمالي الإيرادات</p>
                <p className="text-2xl font-bold text-gray-900">{totalRevenue.toFixed(2)} ر.س</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-2 bg-orange-100 rounded-lg">
                <Users className="h-6 w-6 text-orange-600" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">متوسط الإنفاق</p>
                <p className="text-2xl font-bold text-gray-900">{averageSpending.toFixed(2)} ر.س</p>
              </div>
            </div>
          </div>
        </div>

        {/* Filters and Search */}
        <div className="bg-white rounded-lg shadow p-6 mb-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="form-label">البحث</label>
              <div className="relative">
                <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <input
                  type="text"
                  placeholder="اسم العميل، البريد الإلكتروني، أو رقم الهاتف..."
                  className="form-input pr-10"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
            </div>

            <div>
              <label className="form-label">ترتيب حسب</label>
              <select
                className="form-input"
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value as any)}
              >
                <option value="name">الاسم</option>
                <option value="total_purchases">إجمالي المشتريات</option>
                <option value="last_purchase_date">آخر عملية شراء</option>
                <option value="created_at">تاريخ التسجيل</option>
              </select>
            </div>

            <div className="flex items-end">
              <button
                onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
                className="btn-secondary"
              >
                {sortOrder === 'asc' ? '↑ تصاعدي' : '↓ تنازلي'}
              </button>
            </div>
          </div>
        </div>

        {/* Customers Table */}
        <div className="bg-white rounded-lg shadow overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    العميل
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    معلومات الاتصال
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    إجمالي المشتريات
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    آخر عملية شراء
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    تاريخ التسجيل
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    الإجراءات
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredCustomers.map((customer) => (
                  <tr key={customer.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="h-10 w-10 bg-blue-100 rounded-full flex items-center justify-center ml-4">
                          <span className="text-blue-600 font-medium text-sm">
                            {customer.name.charAt(0)}
                          </span>
                        </div>
                        <div>
                          <div className="text-sm font-medium text-gray-900">{customer.name}</div>
                          {customer.address && (
                            <div className="text-sm text-gray-500 flex items-center">
                              <MapPin className="h-3 w-3 ml-1" />
                              {customer.address}
                            </div>
                          )}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      <div className="space-y-1">
                        {customer.phone && (
                          <div className="flex items-center">
                            <Phone className="h-3 w-3 ml-1 text-gray-400" />
                            {customer.phone}
                          </div>
                        )}
                        {customer.email && (
                          <div className="flex items-center">
                            <Mail className="h-3 w-3 ml-1 text-gray-400" />
                            {customer.email}
                          </div>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        customer.total_purchases > 1000 
                          ? 'bg-green-100 text-green-800' 
                          : customer.total_purchases > 0
                          ? 'bg-blue-100 text-blue-800'
                          : 'bg-gray-100 text-gray-800'
                      }`}>
                        {customer.total_purchases.toFixed(2)} ر.س
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {formatDate(customer.last_purchase_date)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {formatDate(customer.created_at)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex items-center space-x-2 space-x-reverse">
                        <Link
                          href={`/customers/${customer.id}`}
                          className="text-blue-600 hover:text-blue-900"
                        >
                          <Eye className="h-4 w-4" />
                        </Link>
                        <Link
                          href={`/customers/${customer.id}/edit`}
                          className="text-green-600 hover:text-green-900"
                        >
                          <Edit className="h-4 w-4" />
                        </Link>
                        <button
                          onClick={() => handleDeleteCustomer(customer.id)}
                          className="text-red-600 hover:text-red-900"
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {filteredCustomers.length === 0 && (
            <div className="text-center py-12">
              <Users className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">لا يوجد عملاء</h3>
              <p className="mt-1 text-sm text-gray-500">ابدأ بإضافة عميل جديد</p>
              <div className="mt-6">
                <Link
                  href="/customers/add"
                  className="btn-primary inline-flex items-center"
                >
                  <Plus className="h-4 w-4 ml-2" />
                  إضافة عميل جديد
                </Link>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
