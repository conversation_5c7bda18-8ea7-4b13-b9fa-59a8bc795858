'use client'

import { useState } from 'react'
import Link from 'next/link'
import { 
  ShoppingCart, 
  Package, 
  Users, 
  BarChart3, 
  Settings,
  Store,
  TrendingUp,
  DollarSign
} from 'lucide-react'

export default function Home() {
  const [stats] = useState({
    todaySales: 2450.75,
    totalProducts: 156,
    totalCustomers: 89,
    lowStock: 12
  })

  const menuItems = [
    {
      title: 'نقطة البيع',
      description: 'إجراء عمليات البيع والدفع',
      icon: ShoppingCart,
      href: '/pos',
      color: 'bg-blue-500 hover:bg-blue-600'
    },
    {
      title: 'إدارة المنتجات',
      description: 'إضافة وتعديل المنتجات',
      icon: Package,
      href: '/products',
      color: 'bg-green-500 hover:bg-green-600'
    },
    {
      title: 'إدارة العملاء',
      description: 'بيانات العملاء والمشتريات',
      icon: Users,
      href: '/customers',
      color: 'bg-purple-500 hover:bg-purple-600'
    },
    {
      title: 'التقارير',
      description: 'تقارير المبيعات والأرباح',
      icon: BarChart3,
      href: '/reports',
      color: 'bg-orange-500 hover:bg-orange-600'
    },
    {
      title: 'الإعدادات',
      description: 'إعدادات النظام والمتجر',
      icon: Settings,
      href: '/settings',
      color: 'bg-gray-500 hover:bg-gray-600'
    }
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Store className="h-8 w-8 text-blue-600 ml-3" />
              <h1 className="text-2xl font-bold text-gray-900">نظام نقطة البيع</h1>
            </div>
            <div className="flex items-center space-x-4 space-x-reverse">
              <span className="text-sm text-gray-500">مرحباً بك</span>
              <div className="h-8 w-8 bg-blue-600 rounded-full flex items-center justify-center">
                <span className="text-white text-sm font-medium">أ</span>
              </div>
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 rounded-lg">
                <DollarSign className="h-6 w-6 text-green-600" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">مبيعات اليوم</p>
                <p className="text-2xl font-bold text-gray-900">{stats.todaySales.toFixed(2)} ر.س</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Package className="h-6 w-6 text-blue-600" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">إجمالي المنتجات</p>
                <p className="text-2xl font-bold text-gray-900">{stats.totalProducts}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-2 bg-purple-100 rounded-lg">
                <Users className="h-6 w-6 text-purple-600" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">العملاء</p>
                <p className="text-2xl font-bold text-gray-900">{stats.totalCustomers}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-2 bg-red-100 rounded-lg">
                <TrendingUp className="h-6 w-6 text-red-600" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">مخزون منخفض</p>
                <p className="text-2xl font-bold text-gray-900">{stats.lowStock}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Menu Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {menuItems.map((item, index) => (
            <Link
              key={index}
              href={item.href}
              className="group block"
            >
              <div className={`${item.color} rounded-lg p-6 text-white transition-all duration-200 transform group-hover:scale-105 shadow-lg`}>
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="text-xl font-bold mb-2">{item.title}</h3>
                    <p className="text-blue-100 text-sm">{item.description}</p>
                  </div>
                  <item.icon className="h-12 w-12 opacity-80" />
                </div>
              </div>
            </Link>
          ))}
        </div>

        {/* Quick Actions */}
        <div className="mt-8 bg-white rounded-lg shadow p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">إجراءات سريعة</h2>
          <div className="flex flex-wrap gap-4">
            <Link
              href="/pos"
              className="btn-primary inline-flex items-center"
            >
              <ShoppingCart className="h-4 w-4 ml-2" />
              بدء عملية بيع جديدة
            </Link>
            <Link
              href="/products/add"
              className="btn-secondary inline-flex items-center"
            >
              <Package className="h-4 w-4 ml-2" />
              إضافة منتج جديد
            </Link>
            <Link
              href="/reports"
              className="btn-secondary inline-flex items-center"
            >
              <BarChart3 className="h-4 w-4 ml-2" />
              عرض التقارير
            </Link>
          </div>
        </div>
      </main>
    </div>
  )
}
