#!/bin/bash

echo "========================================"
echo "       نظام نقطة البيع - تشغيل سريع"
echo "========================================"
echo

# التحقق من Node.js
echo "جاري التحقق من Node.js..."
if ! command -v node &> /dev/null; then
    echo "خطأ: Node.js غير مثبت!"
    echo "يرجى تحميل وتثبيت Node.js من: https://nodejs.org"
    exit 1
fi

echo "Node.js مثبت بنجاح! الإصدار: $(node --version)"
echo

# التحقق من npm
if ! command -v npm &> /dev/null; then
    echo "خطأ: npm غير متاح!"
    exit 1
fi

echo "npm متاح! الإصدار: $(npm --version)"
echo

# التحقق من المكتبات
echo "جاري التحقق من المكتبات..."
if [ ! -d "node_modules" ]; then
    echo "جاري تثبيت المكتبات..."
    npm install
    if [ $? -ne 0 ]; then
        echo "خطأ في تثبيت المكتبات!"
        exit 1
    fi
else
    echo "المكتبات مثبتة مسبقاً"
fi

# التحقق من ملف الإعدادات
echo "جاري التحقق من ملف الإعدادات..."
if [ ! -f ".env.local" ]; then
    echo "تحذير: ملف .env.local غير موجود!"
    echo "يرجى نسخ .env.local.example إلى .env.local وإدخال بيانات Supabase"
    echo
    read -p "هل تريد المتابعة بدون إعدادات قاعدة البيانات؟ (y/n): " continue
    if [ "$continue" != "y" ] && [ "$continue" != "Y" ]; then
        echo "يرجى إعداد ملف .env.local أولاً"
        exit 1
    fi
else
    echo "ملف الإعدادات موجود"
fi

echo
echo "اختر طريقة التشغيل:"
echo "1. تطبيق الويب (متصفح)"
echo "2. تطبيق سطح المكتب (Electron)"
echo "3. بناء للإنتاج"
echo "4. إلغاء"
echo

read -p "أدخل اختيارك (1-4): " choice

case $choice in
    1)
        echo "جاري تشغيل تطبيق الويب..."
        echo "سيتم فتح المتصفح على http://localhost:3000"
        if command -v xdg-open &> /dev/null; then
            xdg-open http://localhost:3000 &
        elif command -v open &> /dev/null; then
            open http://localhost:3000 &
        fi
        npm run dev
        ;;
    2)
        echo "جاري تشغيل تطبيق سطح المكتب..."
        npm run electron-dev
        ;;
    3)
        echo "جاري بناء التطبيق للإنتاج..."
        npm run build
        echo "تم بناء التطبيق بنجاح في مجلد 'out'"
        ;;
    4)
        echo "تم الإلغاء"
        exit 0
        ;;
    *)
        echo "اختيار غير صحيح!"
        exit 1
        ;;
esac
