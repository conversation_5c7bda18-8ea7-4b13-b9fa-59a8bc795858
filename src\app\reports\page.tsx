'use client'

import { useState, useEffect } from 'react'
import { 
  BarChart3, 
  TrendingUp, 
  DollarSign, 
  Calendar,
  Download,
  Filter,
  Eye,
  Package,
  Users,
  ShoppingCart
} from 'lucide-react'
import Link from 'next/link'

interface SalesData {
  date: string
  sales: number
  profit: number
  orders: number
}

interface TopProduct {
  id: string
  name: string
  quantity_sold: number
  revenue: number
}

export default function ReportsPage() {
  const [selectedPeriod, setSelectedPeriod] = useState<'today' | 'week' | 'month' | 'year'>('today')
  const [salesData, setSalesData] = useState<SalesData[]>([])
  const [topProducts, setTopProducts] = useState<TopProduct[]>([])

  // بيانات تجريبية
  useEffect(() => {
    const mockSalesData: SalesData[] = [
      { date: '2024-01-15', sales: 2450.75, profit: 850.25, orders: 45 },
      { date: '2024-01-14', sales: 1890.50, profit: 650.80, orders: 32 },
      { date: '2024-01-13', sales: 3200.25, profit: 1120.15, orders: 58 },
      { date: '2024-01-12', sales: 2100.00, profit: 735.00, orders: 38 },
      { date: '2024-01-11', sales: 1750.75, profit: 612.75, orders: 29 },
      { date: '2024-01-10', sales: 2890.50, profit: 1011.68, orders: 52 },
      { date: '2024-01-09', sales: 2340.25, profit: 819.09, orders: 41 }
    ]

    const mockTopProducts: TopProduct[] = [
      { id: '1', name: 'قهوة عربية', quantity_sold: 125, revenue: 3125.00 },
      { id: '2', name: 'شاي أحمر', quantity_sold: 98, revenue: 1470.00 },
      { id: '3', name: 'عصير برتقال', quantity_sold: 87, revenue: 739.50 },
      { id: '4', name: 'ماء معدني', quantity_sold: 156, revenue: 312.00 },
      { id: '5', name: 'بسكويت', quantity_sold: 67, revenue: 804.00 }
    ]

    setSalesData(mockSalesData)
    setTopProducts(mockTopProducts)
  }, [selectedPeriod])

  const totalSales = salesData.reduce((sum, day) => sum + day.sales, 0)
  const totalProfit = salesData.reduce((sum, day) => sum + day.profit, 0)
  const totalOrders = salesData.reduce((sum, day) => sum + day.orders, 0)
  const averageOrderValue = totalOrders > 0 ? totalSales / totalOrders : 0
  const profitMargin = totalSales > 0 ? (totalProfit / totalSales) * 100 : 0

  const formatCurrency = (amount: number) => `${amount.toFixed(2)} ر.س`
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ar-SA', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  const getPeriodLabel = () => {
    switch (selectedPeriod) {
      case 'today': return 'اليوم'
      case 'week': return 'هذا الأسبوع'
      case 'month': return 'هذا الشهر'
      case 'year': return 'هذا العام'
      default: return 'اليوم'
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Link href="/" className="text-blue-600 hover:text-blue-800 ml-4">
                ← العودة للرئيسية
              </Link>
              <h1 className="text-xl font-bold text-gray-900">التقارير والإحصائيات</h1>
            </div>
            <div className="flex items-center space-x-4 space-x-reverse">
              <select
                className="form-input"
                value={selectedPeriod}
                onChange={(e) => setSelectedPeriod(e.target.value as any)}
              >
                <option value="today">اليوم</option>
                <option value="week">هذا الأسبوع</option>
                <option value="month">هذا الشهر</option>
                <option value="year">هذا العام</option>
              </select>
              <button className="btn-secondary inline-flex items-center">
                <Download className="h-4 w-4 ml-2" />
                تصدير
              </button>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 rounded-lg">
                <DollarSign className="h-6 w-6 text-green-600" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">إجمالي المبيعات</p>
                <p className="text-2xl font-bold text-gray-900">{formatCurrency(totalSales)}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 rounded-lg">
                <TrendingUp className="h-6 w-6 text-blue-600" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">إجمالي الأرباح</p>
                <p className="text-2xl font-bold text-gray-900">{formatCurrency(totalProfit)}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-2 bg-purple-100 rounded-lg">
                <ShoppingCart className="h-6 w-6 text-purple-600" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">عدد الطلبات</p>
                <p className="text-2xl font-bold text-gray-900">{totalOrders}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-2 bg-orange-100 rounded-lg">
                <BarChart3 className="h-6 w-6 text-orange-600" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">متوسط قيمة الطلب</p>
                <p className="text-2xl font-bold text-gray-900">{formatCurrency(averageOrderValue)}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-2 bg-red-100 rounded-lg">
                <TrendingUp className="h-6 w-6 text-red-600" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">هامش الربح</p>
                <p className="text-2xl font-bold text-gray-900">{profitMargin.toFixed(1)}%</p>
              </div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
          {/* Sales Chart */}
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-lg font-semibold text-gray-900">مبيعات {getPeriodLabel()}</h2>
              <div className="flex items-center space-x-2 space-x-reverse">
                <div className="flex items-center">
                  <div className="w-3 h-3 bg-blue-500 rounded-full ml-2"></div>
                  <span className="text-sm text-gray-600">المبيعات</span>
                </div>
                <div className="flex items-center">
                  <div className="w-3 h-3 bg-green-500 rounded-full ml-2"></div>
                  <span className="text-sm text-gray-600">الأرباح</span>
                </div>
              </div>
            </div>
            
            {/* Simple Bar Chart Representation */}
            <div className="space-y-4">
              {salesData.slice(0, 7).map((day, index) => (
                <div key={day.date} className="flex items-center">
                  <div className="w-20 text-sm text-gray-600">
                    {formatDate(day.date)}
                  </div>
                  <div className="flex-1 mx-4">
                    <div className="flex items-center space-x-2 space-x-reverse">
                      <div className="flex-1 bg-gray-200 rounded-full h-4 relative">
                        <div 
                          className="bg-blue-500 h-4 rounded-full"
                          style={{ width: `${(day.sales / Math.max(...salesData.map(d => d.sales))) * 100}%` }}
                        ></div>
                        <div 
                          className="bg-green-500 h-2 rounded-full absolute top-1"
                          style={{ width: `${(day.profit / Math.max(...salesData.map(d => d.sales))) * 100}%` }}
                        ></div>
                      </div>
                    </div>
                  </div>
                  <div className="w-24 text-sm text-gray-900 text-left">
                    {formatCurrency(day.sales)}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Top Products */}
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-lg font-semibold text-gray-900">أفضل المنتجات مبيعاً</h2>
              <Link href="/products" className="text-blue-600 hover:text-blue-800 text-sm">
                عرض الكل
              </Link>
            </div>
            
            <div className="space-y-4">
              {topProducts.map((product, index) => (
                <div key={product.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center">
                    <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center ml-3">
                      <span className="text-blue-600 font-medium text-sm">{index + 1}</span>
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-900">{product.name}</h4>
                      <p className="text-sm text-gray-600">{product.quantity_sold} وحدة مباعة</p>
                    </div>
                  </div>
                  <div className="text-left">
                    <p className="font-medium text-gray-900">{formatCurrency(product.revenue)}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Detailed Reports */}
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-lg font-semibold text-gray-900">تقرير مفصل - {getPeriodLabel()}</h2>
            <div className="flex items-center space-x-4 space-x-reverse">
              <button className="btn-secondary inline-flex items-center">
                <Filter className="h-4 w-4 ml-2" />
                تصفية
              </button>
              <button className="btn-secondary inline-flex items-center">
                <Download className="h-4 w-4 ml-2" />
                تصدير PDF
              </button>
            </div>
          </div>

          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    التاريخ
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    المبيعات
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    الأرباح
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    عدد الطلبات
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    متوسط قيمة الطلب
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    هامش الربح
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {salesData.map((day) => {
                  const dayAverageOrder = day.orders > 0 ? day.sales / day.orders : 0
                  const dayProfitMargin = day.sales > 0 ? (day.profit / day.sales) * 100 : 0
                  
                  return (
                    <tr key={day.date} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {formatDate(day.date)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {formatCurrency(day.sales)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-green-600 font-medium">
                        {formatCurrency(day.profit)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {day.orders}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {formatCurrency(dayAverageOrder)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          dayProfitMargin >= 30 
                            ? 'bg-green-100 text-green-800' 
                            : dayProfitMargin >= 20
                            ? 'bg-yellow-100 text-yellow-800'
                            : 'bg-red-100 text-red-800'
                        }`}>
                          {dayProfitMargin.toFixed(1)}%
                        </span>
                      </td>
                    </tr>
                  )
                })}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  )
}
