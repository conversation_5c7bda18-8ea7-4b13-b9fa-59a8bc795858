'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { 
  Package, 
  Save, 
  X,
  Upload,
  Barcode
} from 'lucide-react'
import Link from 'next/link'
import toast from 'react-hot-toast'

interface ProductForm {
  name: string
  description: string
  price: number
  cost: number
  stock_quantity: number
  barcode: string
  category_id: string
  image_url: string
}

export default function AddProductPage() {
  const router = useRouter()
  const [loading, setLoading] = useState(false)
  const [formData, setFormData] = useState<ProductForm>({
    name: '',
    description: '',
    price: 0,
    cost: 0,
    stock_quantity: 0,
    barcode: '',
    category_id: '',
    image_url: ''
  })

  // بيانات تجريبية للفئات
  const categories = [
    { id: '1', name: 'مشروبات' },
    { id: '2', name: 'وجبات خفيفة' },
    { id: '3', name: 'حلويات' },
    { id: '4', name: 'منظفات' },
  ]

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: type === 'number' ? parseFloat(value) || 0 : value
    }))
  }

  const generateBarcode = () => {
    const barcode = Math.floor(Math.random() * 9000000000000) + 1000000000000
    setFormData(prev => ({ ...prev, barcode: barcode.toString() }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!formData.name.trim()) {
      toast.error('اسم المنتج مطلوب')
      return
    }

    if (formData.price <= 0) {
      toast.error('سعر البيع يجب أن يكون أكبر من صفر')
      return
    }

    if (formData.cost < 0) {
      toast.error('تكلفة المنتج لا يمكن أن تكون سالبة')
      return
    }

    if (formData.stock_quantity < 0) {
      toast.error('كمية المخزون لا يمكن أن تكون سالبة')
      return
    }

    setLoading(true)

    try {
      // هنا يمكن إضافة منطق حفظ المنتج في قاعدة البيانات
      // await dbHelpers.createProduct(formData)
      
      // محاكاة تأخير الشبكة
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      toast.success('تم إضافة المنتج بنجاح!')
      router.push('/products')
    } catch (error) {
      toast.error('حدث خطأ أثناء إضافة المنتج')
      console.error('Error adding product:', error)
    } finally {
      setLoading(false)
    }
  }

  const profit = formData.price - formData.cost
  const profitMargin = formData.price > 0 ? ((profit / formData.price) * 100) : 0

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Link href="/products" className="text-blue-600 hover:text-blue-800 ml-4">
                ← العودة للمنتجات
              </Link>
              <h1 className="text-xl font-bold text-gray-900">إضافة منتج جديد</h1>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Main Form */}
            <div className="lg:col-span-2 space-y-6">
              {/* Basic Information */}
              <div className="bg-white rounded-lg shadow p-6">
                <h2 className="text-lg font-semibold text-gray-900 mb-4">المعلومات الأساسية</h2>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="md:col-span-2">
                    <label className="form-label">اسم المنتج *</label>
                    <input
                      type="text"
                      name="name"
                      required
                      className="form-input"
                      value={formData.name}
                      onChange={handleInputChange}
                      placeholder="أدخل اسم المنتج"
                    />
                  </div>

                  <div className="md:col-span-2">
                    <label className="form-label">الوصف</label>
                    <textarea
                      name="description"
                      rows={3}
                      className="form-input"
                      value={formData.description}
                      onChange={handleInputChange}
                      placeholder="وصف المنتج (اختياري)"
                    />
                  </div>

                  <div>
                    <label className="form-label">الفئة</label>
                    <select
                      name="category_id"
                      className="form-input"
                      value={formData.category_id}
                      onChange={handleInputChange}
                    >
                      <option value="">اختر الفئة</option>
                      {categories.map(category => (
                        <option key={category.id} value={category.id}>
                          {category.name}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="form-label">الباركود</label>
                    <div className="flex">
                      <input
                        type="text"
                        name="barcode"
                        className="form-input rounded-l-none"
                        value={formData.barcode}
                        onChange={handleInputChange}
                        placeholder="الباركود"
                      />
                      <button
                        type="button"
                        onClick={generateBarcode}
                        className="px-3 py-2 bg-gray-200 border border-r-0 border-gray-300 rounded-r-md hover:bg-gray-300 transition-colors"
                      >
                        <Barcode className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              {/* Pricing */}
              <div className="bg-white rounded-lg shadow p-6">
                <h2 className="text-lg font-semibold text-gray-900 mb-4">التسعير والمخزون</h2>
                
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="form-label">تكلفة المنتج (ر.س) *</label>
                    <input
                      type="number"
                      name="cost"
                      step="0.01"
                      min="0"
                      required
                      className="form-input"
                      value={formData.cost}
                      onChange={handleInputChange}
                      placeholder="0.00"
                    />
                  </div>

                  <div>
                    <label className="form-label">سعر البيع (ر.س) *</label>
                    <input
                      type="number"
                      name="price"
                      step="0.01"
                      min="0.01"
                      required
                      className="form-input"
                      value={formData.price}
                      onChange={handleInputChange}
                      placeholder="0.00"
                    />
                  </div>

                  <div>
                    <label className="form-label">كمية المخزون *</label>
                    <input
                      type="number"
                      name="stock_quantity"
                      min="0"
                      required
                      className="form-input"
                      value={formData.stock_quantity}
                      onChange={handleInputChange}
                      placeholder="0"
                    />
                  </div>
                </div>

                {/* Profit Calculation */}
                {formData.price > 0 && (
                  <div className="mt-4 p-4 bg-gray-50 rounded-lg">
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-gray-600">الربح لكل وحدة:</span>
                        <span className={`font-medium mr-2 ${profit >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                          {profit.toFixed(2)} ر.س
                        </span>
                      </div>
                      <div>
                        <span className="text-gray-600">هامش الربح:</span>
                        <span className={`font-medium mr-2 ${profitMargin >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                          {profitMargin.toFixed(1)}%
                        </span>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {/* Image Upload */}
              <div className="bg-white rounded-lg shadow p-6">
                <h2 className="text-lg font-semibold text-gray-900 mb-4">صورة المنتج</h2>
                
                <div>
                  <label className="form-label">رابط الصورة</label>
                  <input
                    type="url"
                    name="image_url"
                    className="form-input"
                    value={formData.image_url}
                    onChange={handleInputChange}
                    placeholder="https://example.com/image.jpg"
                  />
                  <p className="text-sm text-gray-500 mt-1">
                    يمكنك إدخال رابط صورة من الإنترنت أو رفع صورة لاحقاً
                  </p>
                </div>

                {formData.image_url && (
                  <div className="mt-4">
                    <img
                      src={formData.image_url}
                      alt="معاينة المنتج"
                      className="h-32 w-32 object-cover rounded-lg border"
                      onError={(e) => {
                        e.currentTarget.style.display = 'none'
                      }}
                    />
                  </div>
                )}
              </div>
            </div>

            {/* Sidebar */}
            <div className="lg:col-span-1">
              <div className="bg-white rounded-lg shadow p-6 sticky top-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">ملخص المنتج</h3>
                
                <div className="space-y-3 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">الاسم:</span>
                    <span className="font-medium">{formData.name || '-'}</span>
                  </div>
                  
                  <div className="flex justify-between">
                    <span className="text-gray-600">الفئة:</span>
                    <span className="font-medium">
                      {categories.find(c => c.id === formData.category_id)?.name || '-'}
                    </span>
                  </div>
                  
                  <div className="flex justify-between">
                    <span className="text-gray-600">التكلفة:</span>
                    <span className="font-medium">{formData.cost.toFixed(2)} ر.س</span>
                  </div>
                  
                  <div className="flex justify-between">
                    <span className="text-gray-600">سعر البيع:</span>
                    <span className="font-medium">{formData.price.toFixed(2)} ر.س</span>
                  </div>
                  
                  <div className="flex justify-between">
                    <span className="text-gray-600">المخزون:</span>
                    <span className="font-medium">{formData.stock_quantity}</span>
                  </div>
                  
                  <hr />
                  
                  <div className="flex justify-between">
                    <span className="text-gray-600">الربح:</span>
                    <span className={`font-medium ${profit >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                      {profit.toFixed(2)} ر.س
                    </span>
                  </div>
                  
                  <div className="flex justify-between">
                    <span className="text-gray-600">هامش الربح:</span>
                    <span className={`font-medium ${profitMargin >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                      {profitMargin.toFixed(1)}%
                    </span>
                  </div>
                </div>

                <div className="mt-6 space-y-3">
                  <button
                    type="submit"
                    disabled={loading}
                    className="w-full btn-primary flex items-center justify-center"
                  >
                    {loading ? (
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    ) : (
                      <>
                        <Save className="h-4 w-4 ml-2" />
                        حفظ المنتج
                      </>
                    )}
                  </button>
                  
                  <Link
                    href="/products"
                    className="w-full btn-secondary flex items-center justify-center"
                  >
                    <X className="h-4 w-4 ml-2" />
                    إلغاء
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  )
}
