import type { Metadata } from 'next'
import './globals.css'
import { Toaster } from 'react-hot-toast'

export const metadata: Metadata = {
  title: 'نظام نقطة البيع',
  description: 'نظام نقطة بيع شامل ومتطور',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="ar" dir="rtl">
      <body className="font-arabic">
        <div className="min-h-screen bg-gray-50">
          {children}
        </div>
        <Toaster 
          position="top-center"
          toastOptions={{
            duration: 4000,
            style: {
              background: '#363636',
              color: '#fff',
              fontFamily: 'Cairo, Tajawal, sans-serif',
              direction: 'rtl',
            },
          }}
        />
      </body>
    </html>
  )
}
