-- نظام نقطة البيع - قاعدة البيانات
-- تشغيل هذا الملف في SQL Editor في Supabase

-- تفعيل Row Level Security
ALTER DATABASE postgres SET "app.jwt_secret" TO 'your-jwt-secret';

-- جدول الفئات
CREATE TABLE IF NOT EXISTS categories (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- جدول المنتجات
CREATE TABLE IF NOT EXISTS products (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  price DECIMAL(10,2) NOT NULL CHECK (price >= 0),
  cost DECIMAL(10,2) DEFAULT 0 CHECK (cost >= 0),
  stock_quantity INTEGER DEFAULT 0 CHECK (stock_quantity >= 0),
  barcode VARCHAR(255) UNIQUE,
  category_id UUID REFERENCES categories(id) ON DELETE SET NULL,
  image_url TEXT,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- جدول العملاء
CREATE TABLE IF NOT EXISTS customers (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  email VARCHAR(255) UNIQUE,
  phone VARCHAR(50),
  address TEXT,
  date_of_birth DATE,
  notes TEXT,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- جدول المبيعات
CREATE TABLE IF NOT EXISTS sales (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  customer_id UUID REFERENCES customers(id) ON DELETE SET NULL,
  user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  subtotal DECIMAL(10,2) NOT NULL DEFAULT 0,
  discount_amount DECIMAL(10,2) DEFAULT 0 CHECK (discount_amount >= 0),
  tax_amount DECIMAL(10,2) DEFAULT 0 CHECK (tax_amount >= 0),
  total_amount DECIMAL(10,2) NOT NULL CHECK (total_amount >= 0),
  payment_method VARCHAR(50) NOT NULL CHECK (payment_method IN ('cash', 'card', 'transfer')),
  payment_status VARCHAR(50) DEFAULT 'completed' CHECK (payment_status IN ('pending', 'completed', 'cancelled', 'refunded')),
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- جدول عناصر المبيعات
CREATE TABLE IF NOT EXISTS sale_items (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  sale_id UUID REFERENCES sales(id) ON DELETE CASCADE,
  product_id UUID REFERENCES products(id) ON DELETE RESTRICT,
  quantity INTEGER NOT NULL CHECK (quantity > 0),
  unit_price DECIMAL(10,2) NOT NULL CHECK (unit_price >= 0),
  total_price DECIMAL(10,2) NOT NULL CHECK (total_price >= 0),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- جدول المرتجعات
CREATE TABLE IF NOT EXISTS returns (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  sale_id UUID REFERENCES sales(id) ON DELETE CASCADE,
  customer_id UUID REFERENCES customers(id) ON DELETE SET NULL,
  user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  total_amount DECIMAL(10,2) NOT NULL CHECK (total_amount >= 0),
  reason TEXT,
  status VARCHAR(50) DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- جدول عناصر المرتجعات
CREATE TABLE IF NOT EXISTS return_items (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  return_id UUID REFERENCES returns(id) ON DELETE CASCADE,
  product_id UUID REFERENCES products(id) ON DELETE RESTRICT,
  quantity INTEGER NOT NULL CHECK (quantity > 0),
  unit_price DECIMAL(10,2) NOT NULL CHECK (unit_price >= 0),
  total_price DECIMAL(10,2) NOT NULL CHECK (total_price >= 0),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- جدول حركات المخزون
CREATE TABLE IF NOT EXISTS inventory_movements (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  product_id UUID REFERENCES products(id) ON DELETE CASCADE,
  movement_type VARCHAR(50) NOT NULL CHECK (movement_type IN ('in', 'out', 'adjustment')),
  quantity INTEGER NOT NULL,
  reference_type VARCHAR(50), -- 'sale', 'return', 'adjustment', 'initial'
  reference_id UUID,
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- جدول الإعدادات
CREATE TABLE IF NOT EXISTS settings (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  key VARCHAR(255) UNIQUE NOT NULL,
  value TEXT,
  description TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إدراج الإعدادات الافتراضية
INSERT INTO settings (key, value, description) VALUES
('store_name', 'متجر الأمل', 'اسم المتجر'),
('store_address', 'الرياض، المملكة العربية السعودية', 'عنوان المتجر'),
('store_phone', '0112345678', 'رقم هاتف المتجر'),
('store_email', '<EMAIL>', 'البريد الإلكتروني للمتجر'),
('tax_rate', '15', 'معدل الضريبة بالنسبة المئوية'),
('currency', 'SAR', 'العملة المستخدمة'),
('low_stock_threshold', '10', 'حد المخزون المنخفض'),
('receipt_footer', 'شكراً لزيارتكم', 'نص أسفل الفاتورة')
ON CONFLICT (key) DO NOTHING;

-- إنشاء الفهارس لتحسين الأداء
CREATE INDEX IF NOT EXISTS idx_products_category ON products(category_id);
CREATE INDEX IF NOT EXISTS idx_products_barcode ON products(barcode);
CREATE INDEX IF NOT EXISTS idx_products_active ON products(is_active);
CREATE INDEX IF NOT EXISTS idx_sales_customer ON sales(customer_id);
CREATE INDEX IF NOT EXISTS idx_sales_date ON sales(created_at);
CREATE INDEX IF NOT EXISTS idx_sale_items_sale ON sale_items(sale_id);
CREATE INDEX IF NOT EXISTS idx_sale_items_product ON sale_items(product_id);
CREATE INDEX IF NOT EXISTS idx_inventory_product ON inventory_movements(product_id);
CREATE INDEX IF NOT EXISTS idx_inventory_date ON inventory_movements(created_at);

-- دالة لتحديث المخزون عند البيع
CREATE OR REPLACE FUNCTION update_product_stock(product_id UUID, quantity_sold INTEGER)
RETURNS VOID AS $$
BEGIN
  -- تحديث كمية المخزون
  UPDATE products 
  SET stock_quantity = stock_quantity - quantity_sold,
      updated_at = NOW()
  WHERE id = product_id;
  
  -- إضافة حركة مخزون
  INSERT INTO inventory_movements (product_id, movement_type, quantity, reference_type)
  VALUES (product_id, 'out', quantity_sold, 'sale');
END;
$$ LANGUAGE plpgsql;

-- دالة لإضافة مخزون
CREATE OR REPLACE FUNCTION add_product_stock(product_id UUID, quantity_added INTEGER, notes TEXT DEFAULT NULL)
RETURNS VOID AS $$
BEGIN
  -- تحديث كمية المخزون
  UPDATE products 
  SET stock_quantity = stock_quantity + quantity_added,
      updated_at = NOW()
  WHERE id = product_id;
  
  -- إضافة حركة مخزون
  INSERT INTO inventory_movements (product_id, movement_type, quantity, reference_type, notes)
  VALUES (product_id, 'in', quantity_added, 'adjustment', notes);
END;
$$ LANGUAGE plpgsql;

-- دالة لحساب إجمالي المبيعات
CREATE OR REPLACE FUNCTION calculate_sales_total(start_date DATE, end_date DATE)
RETURNS TABLE(
  total_sales DECIMAL,
  total_profit DECIMAL,
  total_orders BIGINT
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    COALESCE(SUM(s.total_amount), 0) as total_sales,
    COALESCE(SUM(si.total_price - (p.cost * si.quantity)), 0) as total_profit,
    COUNT(DISTINCT s.id) as total_orders
  FROM sales s
  LEFT JOIN sale_items si ON s.id = si.sale_id
  LEFT JOIN products p ON si.product_id = p.id
  WHERE s.created_at::date BETWEEN start_date AND end_date
    AND s.payment_status = 'completed';
END;
$$ LANGUAGE plpgsql;

-- دالة للحصول على المنتجات الأكثر مبيعاً
CREATE OR REPLACE FUNCTION get_top_selling_products(limit_count INTEGER DEFAULT 10)
RETURNS TABLE(
  product_id UUID,
  product_name VARCHAR,
  total_quantity BIGINT,
  total_revenue DECIMAL
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    p.id as product_id,
    p.name as product_name,
    SUM(si.quantity) as total_quantity,
    SUM(si.total_price) as total_revenue
  FROM products p
  JOIN sale_items si ON p.id = si.product_id
  JOIN sales s ON si.sale_id = s.id
  WHERE s.payment_status = 'completed'
  GROUP BY p.id, p.name
  ORDER BY total_quantity DESC
  LIMIT limit_count;
END;
$$ LANGUAGE plpgsql;

-- تفعيل Row Level Security
ALTER TABLE categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE products ENABLE ROW LEVEL SECURITY;
ALTER TABLE customers ENABLE ROW LEVEL SECURITY;
ALTER TABLE sales ENABLE ROW LEVEL SECURITY;
ALTER TABLE sale_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE returns ENABLE ROW LEVEL SECURITY;
ALTER TABLE return_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE inventory_movements ENABLE ROW LEVEL SECURITY;
ALTER TABLE settings ENABLE ROW LEVEL SECURITY;

-- سياسات الأمان (يمكن تخصيصها حسب الحاجة)
-- السماح للمستخدمين المصادق عليهم بالوصول لجميع البيانات
CREATE POLICY "Enable all operations for authenticated users" ON categories FOR ALL TO authenticated USING (true);
CREATE POLICY "Enable all operations for authenticated users" ON products FOR ALL TO authenticated USING (true);
CREATE POLICY "Enable all operations for authenticated users" ON customers FOR ALL TO authenticated USING (true);
CREATE POLICY "Enable all operations for authenticated users" ON sales FOR ALL TO authenticated USING (true);
CREATE POLICY "Enable all operations for authenticated users" ON sale_items FOR ALL TO authenticated USING (true);
CREATE POLICY "Enable all operations for authenticated users" ON returns FOR ALL TO authenticated USING (true);
CREATE POLICY "Enable all operations for authenticated users" ON return_items FOR ALL TO authenticated USING (true);
CREATE POLICY "Enable all operations for authenticated users" ON inventory_movements FOR ALL TO authenticated USING (true);
CREATE POLICY "Enable all operations for authenticated users" ON settings FOR ALL TO authenticated USING (true);

-- إدراج بيانات تجريبية للفئات
INSERT INTO categories (name, description) VALUES
('مشروبات', 'جميع أنواع المشروبات الساخنة والباردة'),
('وجبات خفيفة', 'البسكويت والمكسرات والحلويات الخفيفة'),
('حلويات', 'الشوكولاتة والحلويات المختلفة'),
('منظفات', 'مواد التنظيف والعناية الشخصية')
ON CONFLICT DO NOTHING;

-- إدراج بيانات تجريبية للمنتجات
INSERT INTO products (name, description, price, cost, stock_quantity, category_id) 
SELECT 
  'قهوة عربية', 'قهوة عربية أصيلة', 25.00, 15.00, 50, c.id
FROM categories c WHERE c.name = 'مشروبات'
ON CONFLICT DO NOTHING;

INSERT INTO products (name, description, price, cost, stock_quantity, category_id) 
SELECT 
  'شاي أحمر', 'شاي أحمر فاخر', 15.00, 8.00, 30, c.id
FROM categories c WHERE c.name = 'مشروبات'
ON CONFLICT DO NOTHING;

INSERT INTO products (name, description, price, cost, stock_quantity, category_id) 
SELECT 
  'بسكويت', 'بسكويت محلى', 12.00, 7.00, 40, c.id
FROM categories c WHERE c.name = 'وجبات خفيفة'
ON CONFLICT DO NOTHING;
