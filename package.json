{"name": "pos-system", "version": "1.0.0", "description": "نظام نقطة بيع شامل", "main": "electron/main.js", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "electron": "electron .", "electron-dev": "concurrently \"npm run dev\" \"wait-on http://localhost:3000 && electron .\"", "electron-build": "npm run build && electron-builder", "dist": "npm run build && electron-builder --publish=never"}, "keywords": ["pos", "point-of-sale", "electron", "nextjs", "react"], "author": "Your Name", "license": "MIT", "dependencies": {"@supabase/supabase-js": "^2.38.4", "next": "14.0.4", "react": "^18.2.0", "react-dom": "^18.2.0", "@types/node": "^20.10.5", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "typescript": "^5.3.3", "tailwindcss": "^3.3.6", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "lucide-react": "^0.294.0", "react-hot-toast": "^2.4.1", "date-fns": "^2.30.0"}, "devDependencies": {"electron": "^27.1.3", "electron-builder": "^24.8.1", "concurrently": "^8.2.2", "wait-on": "^7.2.0", "eslint": "^8.56.0", "eslint-config-next": "14.0.4"}, "build": {"appId": "com.yourcompany.pos-system", "productName": "نظام نقطة البيع", "directories": {"output": "dist"}, "files": ["out/**/*", "electron/**/*", "package.json"], "mac": {"category": "public.app-category.business"}, "win": {"target": "nsis"}, "linux": {"target": "AppImage"}}}