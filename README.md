# نظام نقطة البيع

نظام نقطة بيع شامل ومتطور مبني باستخدام Next.js و Electron و Supabase.

## الميزات

- 🛒 **نقطة البيع**: واجهة سهلة الاستخدام لإجراء عمليات البيع
- 📦 **إدارة المنتجات**: إضافة وتعديل وحذف المنتجات مع إدارة المخزون
- 👥 **إدارة العملاء**: قاعدة بيانات العملاء وتاريخ المشتريات
- 📊 **التقارير**: تقارير مفصلة للمبيعات والأرباح
- 🖨️ **طباعة الفواتير**: طباعة فواتير احترافية
- 📱 **تطبيق سطح مكتب**: يعمل كتطبيق مستقل على Windows, Mac, Linux
- 🌐 **دعم اللغة العربية**: واجهة مستخدم باللغة العربية بالكامل

## التقنيات المستخدمة

- **Frontend**: Next.js 14 + React + TypeScript
- **Styling**: Tailwind CSS
- **Desktop App**: Electron
- **Database**: Supabase (PostgreSQL)
- **Authentication**: Supabase Auth
- **Icons**: Lucide React

## متطلبات التشغيل

- Node.js 18+ 
- npm أو yarn
- حساب Supabase (مجاني)

## التثبيت والإعداد

### 1. تحميل المشروع

```bash
git clone <repository-url>
cd pos-system
```

### 2. تثبيت المكتبات

```bash
npm install
```

### 3. إعداد قاعدة البيانات

1. إنشاء حساب جديد في [Supabase](https://supabase.com)
2. إنشاء مشروع جديد
3. نسخ URL المشروع و API Key
4. إنشاء ملف `.env.local` ونسخ المحتوى من `.env.local.example`
5. تحديث القيم في `.env.local`

### 4. إنشاء جداول قاعدة البيانات

قم بتشغيل الاستعلامات التالية في SQL Editor في Supabase:

```sql
-- جدول الفئات
CREATE TABLE categories (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- جدول المنتجات
CREATE TABLE products (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  price DECIMAL(10,2) NOT NULL,
  cost DECIMAL(10,2) DEFAULT 0,
  stock_quantity INTEGER DEFAULT 0,
  barcode VARCHAR(255) UNIQUE,
  category_id UUID REFERENCES categories(id),
  image_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- جدول العملاء
CREATE TABLE customers (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  email VARCHAR(255),
  phone VARCHAR(50),
  address TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- جدول المبيعات
CREATE TABLE sales (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  customer_id UUID REFERENCES customers(id),
  total_amount DECIMAL(10,2) NOT NULL,
  discount_amount DECIMAL(10,2) DEFAULT 0,
  tax_amount DECIMAL(10,2) DEFAULT 0,
  payment_method VARCHAR(50) NOT NULL,
  status VARCHAR(50) DEFAULT 'completed',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- جدول عناصر المبيعات
CREATE TABLE sale_items (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  sale_id UUID REFERENCES sales(id) ON DELETE CASCADE,
  product_id UUID REFERENCES products(id),
  quantity INTEGER NOT NULL,
  unit_price DECIMAL(10,2) NOT NULL,
  total_price DECIMAL(10,2) NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- دالة لتحديث المخزون
CREATE OR REPLACE FUNCTION update_product_stock(product_id UUID, quantity_sold INTEGER)
RETURNS VOID AS $$
BEGIN
  UPDATE products 
  SET stock_quantity = stock_quantity - quantity_sold,
      updated_at = NOW()
  WHERE id = product_id;
END;
$$ LANGUAGE plpgsql;
```

### 5. تشغيل التطبيق

#### وضع التطوير (متصفح)
```bash
npm run dev
```

#### تطبيق سطح المكتب (تطوير)
```bash
npm run electron-dev
```

#### بناء التطبيق للإنتاج
```bash
npm run build
npm run electron-build
```

## الاستخدام

1. **الصفحة الرئيسية**: عرض إحصائيات سريعة وروابط للأقسام المختلفة
2. **نقطة البيع**: إضافة منتجات للسلة وإتمام عمليات البيع
3. **إدارة المنتجات**: إضافة وتعديل المنتجات والفئات
4. **إدارة العملاء**: إضافة وإدارة بيانات العملاء
5. **التقارير**: عرض تقارير المبيعات والأرباح

## المساهمة

نرحب بالمساهمات! يرجى:

1. Fork المشروع
2. إنشاء branch جديد للميزة
3. Commit التغييرات
4. Push إلى Branch
5. إنشاء Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## الدعم

إذا واجهت أي مشاكل أو لديك أسئلة، يرجى إنشاء issue في GitHub.
