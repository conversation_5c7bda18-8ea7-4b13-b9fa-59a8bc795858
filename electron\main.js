const { app, BrowserWindow, Menu } = require('electron')
const path = require('path')
const isDev = process.env.NODE_ENV === 'development'

function createWindow() {
  // إنشاء نافذة المتصفح
  const mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    minWidth: 800,
    minHeight: 600,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      webSecurity: true
    },
    icon: path.join(__dirname, 'assets/icon.png'), // يمكن إضافة أيقونة لاحقاً
    titleBarStyle: 'default',
    show: false // لا تظهر النافذة حتى تكون جاهزة
  })

  // تحميل التطبيق
  if (isDev) {
    mainWindow.loadURL('http://localhost:3000')
    // فتح أدوات المطور في وضع التطوير
    mainWindow.webContents.openDevTools()
  } else {
    mainWindow.loadFile(path.join(__dirname, '../out/index.html'))
  }

  // إظهار النافذة عندما تكون جاهزة
  mainWindow.once('ready-to-show', () => {
    mainWindow.show()
    
    // التركيز على النافذة
    if (isDev) {
      mainWindow.focus()
    }
  })

  // إعداد القائمة العربية
  const template = [
    {
      label: 'ملف',
      submenu: [
        {
          label: 'جديد',
          accelerator: 'CmdOrCtrl+N',
          click: () => {
            // يمكن إضافة وظائف هنا
          }
        },
        {
          label: 'حفظ',
          accelerator: 'CmdOrCtrl+S',
          click: () => {
            // يمكن إضافة وظائف هنا
          }
        },
        { type: 'separator' },
        {
          label: 'خروج',
          accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
          click: () => {
            app.quit()
          }
        }
      ]
    },
    {
      label: 'تحرير',
      submenu: [
        { label: 'تراجع', accelerator: 'CmdOrCtrl+Z', role: 'undo' },
        { label: 'إعادة', accelerator: 'Shift+CmdOrCtrl+Z', role: 'redo' },
        { type: 'separator' },
        { label: 'قص', accelerator: 'CmdOrCtrl+X', role: 'cut' },
        { label: 'نسخ', accelerator: 'CmdOrCtrl+C', role: 'copy' },
        { label: 'لصق', accelerator: 'CmdOrCtrl+V', role: 'paste' }
      ]
    },
    {
      label: 'عرض',
      submenu: [
        { label: 'إعادة تحميل', accelerator: 'CmdOrCtrl+R', role: 'reload' },
        { label: 'إعادة تحميل قسري', accelerator: 'CmdOrCtrl+Shift+R', role: 'forceReload' },
        { label: 'أدوات المطور', accelerator: 'F12', role: 'toggleDevTools' },
        { type: 'separator' },
        { label: 'الحجم الفعلي', accelerator: 'CmdOrCtrl+0', role: 'resetZoom' },
        { label: 'تكبير', accelerator: 'CmdOrCtrl+Plus', role: 'zoomIn' },
        { label: 'تصغير', accelerator: 'CmdOrCtrl+-', role: 'zoomOut' },
        { type: 'separator' },
        { label: 'ملء الشاشة', accelerator: 'F11', role: 'togglefullscreen' }
      ]
    },
    {
      label: 'نافذة',
      submenu: [
        { label: 'تصغير', accelerator: 'CmdOrCtrl+M', role: 'minimize' },
        { label: 'إغلاق', accelerator: 'CmdOrCtrl+W', role: 'close' }
      ]
    },
    {
      label: 'مساعدة',
      submenu: [
        {
          label: 'حول التطبيق',
          click: () => {
            // يمكن إضافة نافذة "حول التطبيق" هنا
          }
        }
      ]
    }
  ]

  const menu = Menu.buildFromTemplate(template)
  Menu.setApplicationMenu(menu)
}

// هذا الحدث سيتم إطلاقه عندما يكون Electron جاهزاً
// لإنشاء نوافذ المتصفح.
// بعض واجهات برمجة التطبيقات يمكن استخدامها فقط بعد حدوث هذا الحدث.
app.whenReady().then(createWindow)

// الخروج عندما يتم إغلاق جميع النوافذ، باستثناء macOS.
// في macOS، من الشائع للتطبيقات وشريط القوائم الخاص بها
// أن يبقوا نشطين حتى يقوم المستخدم بالخروج صراحة باستخدام Cmd + Q.
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') app.quit()
})

app.on('activate', () => {
  // في macOS من الشائع إعادة إنشاء نافذة في التطبيق عندما
  // يتم النقر على أيقونة dock ولا توجد نوافذ أخرى مفتوحة.
  if (BrowserWindow.getAllWindows().length === 0) createWindow()
})

// في هذا الملف يمكنك تضمين باقي كود العملية الرئيسية الخاص بتطبيقك.
// يمكنك أيضاً وضعها في ملفات منفصلة وتطلبها هنا.
